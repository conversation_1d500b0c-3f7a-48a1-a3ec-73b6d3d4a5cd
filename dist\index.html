<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knead & Nourish - Coming Soon</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="coming-soon">
                <h2>Coming Soon</h2>              
            </div>
            <div class="logo-container">
                <!-- Replace this with your actual logo -->
                <img src="logo.png" alt="Knead & Nourish Logo" class="logo" id="logo">
                <!-- Fallback text logo if image doesn't load -->
                <div class="text-logo" id="textLogo">
                    <h1>Knead & Nourish</h1>
                </div>
                <p>We're baking up something special for you!</p>
            </div>

        </div>
    </div>

    <!-- Wheat stalks container - stalks will be generated dynamically based on screen width -->
    <div class="wheat-field" id="wheatField">
    </div>

    <script>
        // Show text logo if image fails to load
        function showTextLogo() {
            const logoImg = document.getElementById('logo');
            const textLogo = document.getElementById('textLogo');

            if (logoImg) {
                logoImg.style.display = 'none';
            }
            if (textLogo) {
                textLogo.style.display = 'block';
            }
        }

        // Set up the error handler
        document.addEventListener('DOMContentLoaded', function() {
            const logoImg = document.getElementById('logo');
            if (logoImg) {
                logoImg.onerror = showTextLogo;

                // Also check if the image is already broken (in case the error fired before we set the handler)
                if (logoImg.complete && logoImg.naturalWidth === 0) {
                    showTextLogo();
                }
            }
        });

        // Dynamic wheat field generation and animation
        let mouseX = window.innerWidth / 2; // Start at center
        let mouseY = window.innerHeight; // Start at bottom (full growth)
        let wheatGrowthFactor = 1.0; // Growth factor from 0 (seed) to 1 (full height)
        let wheatStalks = [];
        let stalkData = []; // Store stalk properties
        let gustSystems = [
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 0 },
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 1 },
            { active: false, position: 0, direction: 1, strength: 0, width: 15, speed: 2, nextGustTime: 0, id: 2 }
        ];
        let gustFrequency = {
            baseInterval: 2000, // Base 2 seconds
            variationRange: 4000, // +0 to +4 seconds variation
            lastGustTime: 0,
            minStagger: 800 // Minimum 800ms between gust starts
        };

        // Performance optimization caches
        const mathCache = {
            // Trigonometric lookup tables for common angles (-180 to 180 degrees)
            sinLookup: new Map(),
            cosLookup: new Map(),

            // Growth factor cache
            growthFactorCache: new Map(),

            // Window dimensions cache
            windowDimensions: {
                width: window.innerWidth,
                height: window.innerHeight,
                centerX: window.innerWidth / 2,
                centerZone: window.innerWidth * 0.1,
                seedZone: window.innerHeight * 0.05,
                maxGrowthZone: window.innerHeight * 0.5,
                lastUpdate: Date.now()
            },

            // Wind calculation cache
            windCache: new Map(),

            // Frame-based cache (cleared each frame)
            frameCache: {
                mouseWindStrength: null,
                mouseDirection: null,
                distanceFromCenter: null,
                lastMouseX: null,
                lastMouseY: null
            }
        };

        // Pre-populate trigonometric lookup tables
        function initializeMathCache() {
            // Pre-calculate sin/cos for angles from -180 to 180 degrees with 0.1 degree precision
            for (let angle = -1800; angle <= 1800; angle++) {
                const degrees = angle / 10;
                const radians = (degrees * Math.PI) / 180;
                mathCache.sinLookup.set(degrees, Math.sin(radians));
                mathCache.cosLookup.set(degrees, Math.cos(radians));
            }
        }

        // Fast trigonometric functions using lookup tables
        function fastSin(degrees) {
            // Round to nearest 0.1 degree for lookup
            const roundedDegrees = Math.round(degrees * 10) / 10;
            return mathCache.sinLookup.get(roundedDegrees) || Math.sin((degrees * Math.PI) / 180);
        }

        function fastCos(degrees) {
            // Round to nearest 0.1 degree for lookup
            const roundedDegrees = Math.round(degrees * 10) / 10;
            return mathCache.cosLookup.get(roundedDegrees) || Math.cos((degrees * Math.PI) / 180);
        }

        // Update window dimensions cache
        function updateWindowDimensionsCache() {
            const cache = mathCache.windowDimensions;
            cache.width = window.innerWidth;
            cache.height = window.innerHeight;
            cache.centerX = cache.width / 2;
            cache.centerZone = cache.width * 0.1;
            cache.seedZone = cache.height * 0.05;
            cache.maxGrowthZone = cache.height * 0.5;
            cache.lastUpdate = Date.now();

            // Clear related caches when window dimensions change
            mathCache.growthFactorCache.clear();
            mathCache.windCache.clear();
        }

        // Calculate wheat growth factor based on vertical mouse position (with caching)
        function calculateWheatGrowth(mouseY) {
            // Round mouseY to nearest pixel for cache efficiency
            const roundedMouseY = Math.round(mouseY);

            // Check cache first
            const cacheKey = `${roundedMouseY}_${mathCache.windowDimensions.height}`;
            if (mathCache.growthFactorCache.has(cacheKey)) {
                return mathCache.growthFactorCache.get(cacheKey);
            }

            // Use cached window dimensions
            const windowHeight = mathCache.windowDimensions.height;
            const seedZone = mathCache.windowDimensions.seedZone;
            const maxGrowthZone = mathCache.windowDimensions.maxGrowthZone;

            let growthFactor;

            // If mouse is in bottom 5%, wheat is seed (not visible)
            if (roundedMouseY >= windowHeight - seedZone) {
                growthFactor = 0;
            }
            // If mouse is at or above middle (50%), wheat is at max height
            else if (roundedMouseY <= maxGrowthZone) {
                growthFactor = 1;
            }
            // Calculate growth factor between seed zone and max growth zone
            else {
                const growthRange = (windowHeight - seedZone) - maxGrowthZone;
                const currentPosition = roundedMouseY - maxGrowthZone;
                growthFactor = Math.max(0, Math.min(1, 1 - (currentPosition / growthRange)));
            }

            // Cache the result
            mathCache.growthFactorCache.set(cacheKey, growthFactor);

            // Limit cache size to prevent memory issues
            if (mathCache.growthFactorCache.size > 1000) {
                const firstKey = mathCache.growthFactorCache.keys().next().value;
                mathCache.growthFactorCache.delete(firstKey);
            }

            return growthFactor;
        }

        // Generate wheat stalks based on screen width
        function generateWheatStalks() {
            const wheatField = document.getElementById('wheatField');
            const windowWidth = mathCache.windowDimensions.width;

            // Calculate number of stalks based on screen width (tripled for denser field)
            let stalkCount;
            if (windowWidth >= 1920) {
                stalkCount = 450; // Ultra-wide screens (150 x 3)
            } else if (windowWidth >= 1440) {
                stalkCount = 360; // Large screens (120 x 3)
            } else if (windowWidth >= 1024) {
                stalkCount = 300; // Desktop (100 x 3)
            } else if (windowWidth >= 768) {
                stalkCount = 240; // Tablet (80 x 3)
            } else if (windowWidth >= 480) {
                stalkCount = 180; // Mobile (60 x 3)
            } else {
                stalkCount = 120; // Small mobile (40 x 3)
            }

            // Clear existing stalks
            wheatField.innerHTML = '';
            wheatStalks = [];
            stalkData = [];

            // Generate height variations
            const heights = [68, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95];

            // Create stalks with four-segment structure for realistic bending
            for (let i = 0; i < stalkCount; i++) {
                const stalk = document.createElement('div');
                stalk.className = 'wheat-stalk';
                stalk.setAttribute('data-index', i);

                // Assign random height
                const randomHeight = heights[Math.floor(Math.random() * heights.length)];
                stalk.style.height = randomHeight + 'px';

                // Create four segments for progressive bending
                const baseSegment = document.createElement('div');
                baseSegment.className = 'wheat-segment wheat-base';

                const lowerMiddleSegment = document.createElement('div');
                lowerMiddleSegment.className = 'wheat-segment wheat-lower-middle';

                const upperMiddleSegment = document.createElement('div');
                upperMiddleSegment.className = 'wheat-segment wheat-upper-middle';

                const topSegment = document.createElement('div');
                topSegment.className = 'wheat-segment wheat-top';

                // Calculate segment heights (base: 30%, lower-middle: 25%, upper-middle: 25%, top: 20%)
                const baseHeight = Math.floor(randomHeight * 0.3);
                const lowerMiddleHeight = Math.floor(randomHeight * 0.25);
                const upperMiddleHeight = Math.floor(randomHeight * 0.25);
                const topHeight = randomHeight - baseHeight - lowerMiddleHeight - upperMiddleHeight;

                // Set heights and positions for each segment
                baseSegment.style.height = baseHeight + 'px';
                baseSegment.style.bottom = '0px';

                lowerMiddleSegment.style.height = lowerMiddleHeight + 'px';
                lowerMiddleSegment.style.bottom = baseHeight + 'px';

                upperMiddleSegment.style.height = upperMiddleHeight + 'px';
                upperMiddleSegment.style.bottom = (baseHeight + lowerMiddleHeight) + 'px';

                topSegment.style.height = topHeight + 'px';
                topSegment.style.bottom = (baseHeight + lowerMiddleHeight + upperMiddleHeight) + 'px';

                // Set transform origins for chained movement
                // Base segment pivots from its bottom center (stalk root)
                baseSegment.style.transformOrigin = 'bottom center';

                // Middle and top segments will pivot from their bottom center since we're using translate to position them
                lowerMiddleSegment.style.transformOrigin = 'bottom center';
                upperMiddleSegment.style.transformOrigin = 'bottom center';
                topSegment.style.transformOrigin = 'bottom center';

                // Add all segments as siblings to the stalk container
                stalk.appendChild(baseSegment);
                stalk.appendChild(lowerMiddleSegment);
                stalk.appendChild(upperMiddleSegment);
                stalk.appendChild(topSegment);

                // Store stalk data for wind calculations
                const stalkInfo = {
                    element: stalk,
                    baseSegment: baseSegment,
                    lowerMiddleSegment: lowerMiddleSegment,
                    upperMiddleSegment: upperMiddleSegment,
                    topSegment: topSegment,
                    height: randomHeight,
                    baseHeight: baseHeight,
                    lowerMiddleHeight: lowerMiddleHeight,
                    upperMiddleHeight: upperMiddleHeight,
                    topHeight: topHeight,
                    flexibility: randomHeight / 95, // Taller stalks are more flexible (0.7 to 1.0)
                    naturalSway: Math.random() * 0.3 + 0.8, // Individual character (0.8 to 1.1)
                    position: i / stalkCount, // Position across screen (0 to 1)
                    // Sequential growth completion flags for performance optimization
                    baseSegmentComplete: false,
                    lowerMiddleSegmentComplete: false,
                    upperMiddleSegmentComplete: false,
                    lastGrowthFactor: -1 // Track last growth factor to detect direction changes
                };

                wheatField.appendChild(stalk);
                wheatStalks.push(stalk);
                stalkData.push(stalkInfo);
            }
        }

        // Mouse tracking
        document.addEventListener('mousemove', function(e) {
            mouseX = e.clientX;
            mouseY = e.clientY;
            wheatGrowthFactor = calculateWheatGrowth(mouseY);
        });

        // Touch tracking for mobile devices
        let isTouch = false;

        document.addEventListener('touchstart', function(e) {
            isTouch = true;
            if (e.touches.length > 0) {
                mouseX = e.touches[0].clientX;
                mouseY = e.touches[0].clientY;
                wheatGrowthFactor = calculateWheatGrowth(mouseY);
            }
            // Prevent default to avoid scrolling and other touch behaviors
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('touchmove', function(e) {
            if (e.touches.length > 0) {
                mouseX = e.touches[0].clientX;
                mouseY = e.touches[0].clientY;
                wheatGrowthFactor = calculateWheatGrowth(mouseY);
            }
            // Prevent default to avoid scrolling and other touch behaviors
            e.preventDefault();
        }, { passive: false });

        document.addEventListener('touchend', function(e) {
            // Return horizontal position to center/neutral when finger is lifted
            mouseX = window.innerWidth / 2;
            // Keep vertical position and growth factor at last touch position
            // (wheat height remains at whatever height it was when touch ended)
            // Prevent default to avoid unwanted click events
            e.preventDefault();
        }, { passive: false });

        // Reset touch flag when mouse is used (for hybrid devices)
        document.addEventListener('mouseenter', function() {
            isTouch = false;
        });

        // Enhanced gust system management with multiple simultaneous gusts
        function updateGustSystems() {
            const currentTime = Date.now();

            // Check each gust system
            gustSystems.forEach(gust => {
                // Try to start a new gust if this one is inactive and it's time
                if (!gust.active && currentTime >= gust.nextGustTime) {
                    // Check if enough time has passed since the last gust started (stagger requirement)
                    if (currentTime - gustFrequency.lastGustTime >= gustFrequency.minStagger) {
                        // Start a new gust with varied characteristics
                        gust.active = true;

                        // Vary direction (60% left-to-right, 40% right-to-left for natural feel)
                        const startFromLeft = Math.random() < 0.6;
                        gust.direction = startFromLeft ? 1 : -1;
                        gust.position = startFromLeft ? -25 : 125; // Start further off-screen

                        // Vary strength significantly (0.2 to 1.2 for dramatic range)
                        gust.strength = Math.random() * 1.0 + 0.2;

                        // Vary width based on strength (stronger gusts are wider)
                        const baseWidth = 15 + (gust.strength * 20); // 15-35 stalks
                        gust.width = baseWidth + (Math.random() * 10 - 5); // ±5 variation

                        // Speed correlates with strength (stronger = faster)
                        const baseSpeed = 0.8 + (gust.strength * 2.2); // 1.0 to 3.0
                        gust.speed = baseSpeed + (Math.random() * 0.8 - 0.4); // ±0.4 variation

                        // Set next gust time with high variation
                        const nextInterval = gustFrequency.baseInterval + (Math.random() * gustFrequency.variationRange);
                        gust.nextGustTime = currentTime + nextInterval;

                        // Update last gust start time for staggering
                        gustFrequency.lastGustTime = currentTime;
                    }
                }

                // Update active gusts
                if (gust.active) {
                    // Move the gust across the screen
                    gust.position += gust.direction * gust.speed;

                    // Check if gust has passed through completely
                    const exitThreshold = gust.direction > 0 ? 125 : -25;
                    if ((gust.direction > 0 && gust.position > exitThreshold) ||
                        (gust.direction < 0 && gust.position < exitThreshold)) {
                        gust.active = false;
                    }
                }
            });
        }

        function updateWheatAnimation() {
            // Use cached window dimensions
            const windowWidth = mathCache.windowDimensions.width;
            const centerZone = mathCache.windowDimensions.centerZone;
            const centerX = mathCache.windowDimensions.centerX;

            // Check if mouse position changed since last frame
            let mouseWindStrength, mouseDirection, distanceFromCenter;

            if (mathCache.frameCache.lastMouseX !== mouseX) {
                // Calculate mouse wind effect only if mouse position changed
                distanceFromCenter = Math.abs(mouseX - centerX);
                mouseWindStrength = 0;
                mouseDirection = 0;

                if (distanceFromCenter > centerZone / 2) {
                    mouseDirection = mouseX > centerX ? 1 : -1;
                    const maxDistance = (windowWidth / 2) - (centerZone / 2);
                    mouseWindStrength = Math.min((distanceFromCenter - centerZone / 2) / maxDistance, 1);
                }

                // Cache the results for this frame
                mathCache.frameCache.mouseWindStrength = mouseWindStrength;
                mathCache.frameCache.mouseDirection = mouseDirection;
                mathCache.frameCache.distanceFromCenter = distanceFromCenter;
                mathCache.frameCache.lastMouseX = mouseX;
            } else {
                // Use cached values
                mouseWindStrength = mathCache.frameCache.mouseWindStrength;
                mouseDirection = mathCache.frameCache.mouseDirection;
                distanceFromCenter = mathCache.frameCache.distanceFromCenter;
            }

            // Update all gust systems
            updateGustSystems();

            // Apply growth scaling and wind effects to each stalk with progressive bending
            stalkData.forEach((stalkInfo, index) => {
                // Reset completion flags if growth factor decreased (mouse moved down)
                if (wheatGrowthFactor < stalkInfo.lastGrowthFactor) {
                    if (wheatGrowthFactor <= 0.75) stalkInfo.upperMiddleSegmentComplete = false;
                    if (wheatGrowthFactor <= 0.5) stalkInfo.lowerMiddleSegmentComplete = false;
                    if (wheatGrowthFactor <= 0.25) stalkInfo.baseSegmentComplete = false;
                }
                stalkInfo.lastGrowthFactor = wheatGrowthFactor;

                // Sequential growth optimization: grow segments one at a time for better performance
                let scaledBaseHeight, scaledLowerMiddleHeight, scaledUpperMiddleHeight, scaledTopHeight;
                let baseComplete = false, lowerMiddleComplete = false, upperMiddleComplete = false;

                if (wheatGrowthFactor === 0) {
                    // Seed state - no segments visible
                    scaledBaseHeight = 0;
                    scaledLowerMiddleHeight = 0;
                    scaledUpperMiddleHeight = 0;
                    scaledTopHeight = 0;
                } else if (wheatGrowthFactor <= 0.25) {
                    // Phase 1 (0-25%): Only base segment grows
                    const phaseProgress = wheatGrowthFactor / 0.25; // 0 to 1 within this phase
                    scaledBaseHeight = stalkInfo.baseHeight * phaseProgress;
                    scaledLowerMiddleHeight = 0;
                    scaledUpperMiddleHeight = 0;
                    scaledTopHeight = 0;
                } else if (wheatGrowthFactor <= 0.5) {
                    // Phase 2 (25-50%): Base complete, lower-middle grows
                    baseComplete = true;
                    const phaseProgress = (wheatGrowthFactor - 0.25) / 0.25; // 0 to 1 within this phase
                    scaledBaseHeight = stalkInfo.baseHeight;
                    scaledLowerMiddleHeight = stalkInfo.lowerMiddleHeight * phaseProgress;
                    scaledUpperMiddleHeight = 0;
                    scaledTopHeight = 0;
                } else if (wheatGrowthFactor <= 0.75) {
                    // Phase 3 (50-75%): Base and lower-middle complete, upper-middle grows
                    baseComplete = true;
                    lowerMiddleComplete = true;
                    const phaseProgress = (wheatGrowthFactor - 0.5) / 0.25; // 0 to 1 within this phase
                    scaledBaseHeight = stalkInfo.baseHeight;
                    scaledLowerMiddleHeight = stalkInfo.lowerMiddleHeight;
                    scaledUpperMiddleHeight = stalkInfo.upperMiddleHeight * phaseProgress;
                    scaledTopHeight = 0;
                } else {
                    // Phase 4 (75-100%): All segments complete, top grows
                    baseComplete = true;
                    lowerMiddleComplete = true;
                    upperMiddleComplete = true;
                    const phaseProgress = (wheatGrowthFactor - 0.75) / 0.25; // 0 to 1 within this phase
                    scaledBaseHeight = stalkInfo.baseHeight;
                    scaledLowerMiddleHeight = stalkInfo.lowerMiddleHeight;
                    scaledUpperMiddleHeight = stalkInfo.upperMiddleHeight;
                    scaledTopHeight = stalkInfo.topHeight * phaseProgress;
                }

                // Performance optimization: only update styles for segments that are actively changing
                // Always update base segment
                stalkInfo.baseSegment.style.height = scaledBaseHeight + 'px';
                if (baseComplete) stalkInfo.baseSegmentComplete = true;

                // Update lower-middle segment: show when growing, hide when not in range
                if (wheatGrowthFactor > 0.25 && wheatGrowthFactor <= 0.5) {
                    // Actively growing
                    stalkInfo.lowerMiddleSegment.style.height = scaledLowerMiddleHeight + 'px';
                    stalkInfo.lowerMiddleSegment.style.bottom = scaledBaseHeight + 'px';
                } else if (wheatGrowthFactor > 0.5 && !stalkInfo.lowerMiddleSegmentComplete) {
                    // Just completed
                    stalkInfo.lowerMiddleSegment.style.height = scaledLowerMiddleHeight + 'px';
                    stalkInfo.lowerMiddleSegment.style.bottom = scaledBaseHeight + 'px';
                    stalkInfo.lowerMiddleSegmentComplete = true;
                } else if (wheatGrowthFactor <= 0.25) {
                    // Should be hidden - reset to 0 height
                    stalkInfo.lowerMiddleSegment.style.height = '0px';
                }

                // Update upper-middle segment: show when growing, hide when not in range
                if (wheatGrowthFactor > 0.5 && wheatGrowthFactor <= 0.75) {
                    // Actively growing
                    stalkInfo.upperMiddleSegment.style.height = scaledUpperMiddleHeight + 'px';
                    stalkInfo.upperMiddleSegment.style.bottom = (scaledBaseHeight + scaledLowerMiddleHeight) + 'px';
                } else if (wheatGrowthFactor > 0.75 && !stalkInfo.upperMiddleSegmentComplete) {
                    // Just completed
                    stalkInfo.upperMiddleSegment.style.height = scaledUpperMiddleHeight + 'px';
                    stalkInfo.upperMiddleSegment.style.bottom = (scaledBaseHeight + scaledLowerMiddleHeight) + 'px';
                    stalkInfo.upperMiddleSegmentComplete = true;
                } else if (wheatGrowthFactor <= 0.5) {
                    // Should be hidden - reset to 0 height
                    stalkInfo.upperMiddleSegment.style.height = '0px';
                }

                // Update top segment: show when growing, hide when not in range
                if (wheatGrowthFactor > 0.75) {
                    // Actively growing
                    stalkInfo.topSegment.style.height = scaledTopHeight + 'px';
                    stalkInfo.topSegment.style.bottom = (scaledBaseHeight + scaledLowerMiddleHeight + scaledUpperMiddleHeight) + 'px';
                } else {
                    // Should be hidden - reset to 0 height
                    stalkInfo.topSegment.style.height = '0px';
                }

                // Hide stalks completely when growth factor is 0 (seed state)
                stalkInfo.element.style.opacity = wheatGrowthFactor === 0 ? '0' : '0.9';

                // Handle berry visibility based on growth factor
                // Berries only appear when wheat is between 80% and 100% growth
                const berryThreshold = 0.8; // 80% growth threshold
                let berryOpacity = 0;

                if (wheatGrowthFactor >= berryThreshold) {
                    // Calculate berry opacity based on growth in the final 20% (0.8 to 1.0)
                    const berryGrowthRange = 1.0 - berryThreshold; // 0.2 (20%)
                    const berryProgress = (wheatGrowthFactor - berryThreshold) / berryGrowthRange;
                    berryOpacity = Math.min(1, berryProgress); // 0 to 1
                }

                // Apply berry opacity to the top segment's ::after pseudo-element
                // We need to use CSS custom properties to control the berry opacity
                stalkInfo.topSegment.style.setProperty('--berry-opacity', berryOpacity);

                // Calculate wind effects using scaled heights for realistic physics
                let totalRotation = 0;

                // Only apply wind effects if wheat has grown (growth factor > 0)
                if (wheatGrowthFactor > 0) {
                    // Mouse wind effect
                    if (mouseWindStrength > 0) {
                        const mouseRotation = mouseDirection * mouseWindStrength * 25 * stalkInfo.flexibility * stalkInfo.naturalSway;
                        totalRotation += mouseRotation;
                    }

                    // Multiple gust wind effects (can stack)
                    gustSystems.forEach(gust => {
                        if (gust.active) {
                            const stalkPosition = stalkInfo.position * 100; // Convert to percentage
                            const distanceFromGust = Math.abs(stalkPosition - gust.position);

                            if (distanceFromGust <= gust.width) {
                                // Stalk is within gust range
                                const gustInfluence = Math.max(0, 1 - (distanceFromGust / gust.width));

                                // Enhanced gust effect with strength variation
                                const baseGustRotation = 35; // Increased base rotation for more dramatic effect
                                const strengthMultiplier = Math.pow(gust.strength, 1.5); // Non-linear strength scaling
                                const gustRotation = gust.direction * strengthMultiplier * baseGustRotation * gustInfluence * stalkInfo.flexibility * stalkInfo.naturalSway;

                                totalRotation += gustRotation;
                            }
                        }
                    });

                    // Add subtle natural sway even without wind
                    const naturalSway = Math.sin(Date.now() * 0.001 + index * 0.1) * 2 * stalkInfo.naturalSway;
                    totalRotation += naturalSway;
                }

                // Calculate progressive bending for realistic physics with chained movement (4 segments)
                const baseRotation = totalRotation * 0.2;         // Base: 20% of total rotation
                const lowerMiddleRotation = totalRotation * 0.4;   // Lower-middle: 40% of total rotation
                const upperMiddleRotation = totalRotation * 0.7;   // Upper-middle: 70% of total rotation
                const topRotation = totalRotation;                 // Top: 100% of total rotation

                // Apply dynamic transition based on wind strength
                const windIntensity = Math.abs(totalRotation);
                const transitionSpeed = Math.max(0.1, Math.min(0.5, 0.3 - (windIntensity * 0.005))); // Faster transitions for stronger winds

                // Calculate chained positions for realistic connected movement (4 segments) using scaled heights
                // Base segment rotates from stalk root
                stalkInfo.baseSegment.style.transform = `rotateZ(${baseRotation}deg)`;
                stalkInfo.baseSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the base segment ends up after rotation (using scaled height)
                const baseEndX = fastSin(baseRotation) * scaledBaseHeight;
                const baseEndY = fastCos(baseRotation) * scaledBaseHeight;

                // Position lower-middle segment to start from the end of the base segment (using scaled heights)
                const lowerMiddleOriginalY = scaledBaseHeight;
                const lowerMiddleTranslateX = baseEndX;
                const lowerMiddleTranslateY = lowerMiddleOriginalY - baseEndY;

                stalkInfo.lowerMiddleSegment.style.transform = `translate3d(${lowerMiddleTranslateX}px, ${lowerMiddleTranslateY}px, 0) rotateZ(${lowerMiddleRotation}deg)`;
                stalkInfo.lowerMiddleSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the lower-middle segment ends up after its rotation and translation (using scaled height)
                const lowerMiddleEndX = lowerMiddleTranslateX + fastSin(lowerMiddleRotation) * scaledLowerMiddleHeight;
                const lowerMiddleEndY = baseEndY + fastCos(lowerMiddleRotation) * scaledLowerMiddleHeight;

                // Position upper-middle segment to start from the end of the lower-middle segment (using scaled heights)
                const upperMiddleOriginalY = scaledBaseHeight + scaledLowerMiddleHeight;
                const upperMiddleTranslateX = lowerMiddleEndX;
                const upperMiddleTranslateY = upperMiddleOriginalY - lowerMiddleEndY;

                stalkInfo.upperMiddleSegment.style.transform = `translate3d(${upperMiddleTranslateX}px, ${upperMiddleTranslateY}px, 0) rotateZ(${upperMiddleRotation}deg)`;
                stalkInfo.upperMiddleSegment.style.transition = `transform ${transitionSpeed}s ease-out`;

                // Calculate where the top of the upper-middle segment ends up after its rotation and translation (using scaled height)
                const upperMiddleEndX = upperMiddleTranslateX + fastSin(upperMiddleRotation) * scaledUpperMiddleHeight;
                const upperMiddleEndY = lowerMiddleEndY + fastCos(upperMiddleRotation) * scaledUpperMiddleHeight;

                // Position top segment to start from the end of the upper-middle segment (using scaled heights)
                const topOriginalY = scaledBaseHeight + scaledLowerMiddleHeight + scaledUpperMiddleHeight;
                const topTranslateX = upperMiddleEndX;
                const topTranslateY = topOriginalY - upperMiddleEndY;

                stalkInfo.topSegment.style.transform = `translate3d(${topTranslateX}px, ${topTranslateY}px, 0) rotateZ(${topRotation}deg)`;
                stalkInfo.topSegment.style.transition = `transform ${transitionSpeed}s ease-out`;
            });
        }

        // Animation loop for continuous updates
        function animationLoop() {
            updateWheatAnimation();
            requestAnimationFrame(animationLoop);
        }

        // Handle window resize
        window.addEventListener('resize', function() {
            // Update cached window dimensions first
            updateWindowDimensionsCache();

            generateWheatStalks();
            mouseX = mathCache.windowDimensions.centerX; // Reset horizontal mouse position
            mouseY = mathCache.windowDimensions.height; // Reset vertical mouse position to bottom (full growth)
            wheatGrowthFactor = 1.0; // Reset to full growth

            // Clear frame cache
            mathCache.frameCache.lastMouseX = null;
            mathCache.frameCache.lastMouseY = null;

            // Reset all gust systems
            gustSystems.forEach((gust, index) => {
                gust.active = false;
                gust.nextGustTime = Date.now() + 2000 + (index * gustFrequency.minStagger);
            });
            gustFrequency.lastGustTime = 0;
        });

        // Initialize wheat field
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize performance caches
            initializeMathCache();
            updateWindowDimensionsCache();

            generateWheatStalks();
            // Set initial gust timing with staggered starts
            const currentTime = Date.now();
            gustSystems.forEach((gust, index) => {
                const baseDelay = Math.random() * 4000 + 2000; // 2-6 seconds initial delay
                gust.nextGustTime = currentTime + baseDelay + (index * gustFrequency.minStagger);
            });
            gustFrequency.lastGustTime = currentTime;
            // Start animation loop
            animationLoop();
        });
    </script>
</body>
</html>